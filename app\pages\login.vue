<template>
  <div>
      <div>
    <h2>Sign In</h2>
    
    <form @submit.prevent="handleLogin">
      <div class="form-group">
        <label for="email">Email</label>
        <input 
          id="email"
          v-model="email" 
          type="email" 
          required 
        />
      </div>
      
      <div class="form-group">
        <label for="password">Password</label>
        <input 
          id="password"
          v-model="password" 
          type="password" 
          required 
        />
      </div>
      
      <button type="submit" :disabled="loading">
        {{ loading ? 'Signing in...' : 'Sign In' }}
      </button>
      <NuxtLink to="/login" style="color: #007bff;">Sign in</NuxtLink>
    </form>
    
    <div v-if="error" class="error">
      {{ error }}
    </div>
  </div>
</div>  
</template>

<script lang="ts" setup>
    definePageMeta({
        layout: 'auth'
    })

    const { $supabase } = useNuxtApp()
    const email = ref('')
    const password = ref('')
    const loading = ref(false)
    const error = ref('')

    const handleLogin = async () => {
        loading.value = true
        error.value = ''
        
        const { error: loginError } = await $supabase.auth.signInWithPassword({
            email: email.value,
            password: password.value,
        })
        
        if (loginError) {
            error.value = loginError.message
        } else {
            await navigateTo('/dashboard')
        }
        
        loading.value = false
    }

    
</script>

<style scoped>
    h2 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: #333;
}

.form-group {
  margin-bottom: 1rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

button {
  width: 100%;
  padding: 0.75rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.error {
  margin-top: 1rem;
  padding: 0.75rem;
  background: #fee;
  color: #c33;
  border-radius: 4px;
  text-align: center;
}
</style>
