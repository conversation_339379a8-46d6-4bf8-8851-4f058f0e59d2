<script setup lang="ts">
definePageMeta({
  layout: 'auth'
})

const { $supabase } = useNuxtApp()

const email = ref('')
const password = ref('')
const loading = ref(false)
const error = ref('')
const success = ref('')

const handleRegister = async () => {
  loading.value = true
  error.value = ''
  success.value = ''
  
  const { error: signUpError } = await $supabase.auth.signUp({
    email: email.value,
    password: password.value,
  })
  
  if (signUpError) {
    error.value = signUpError.message
  } else {
    success.value = 'Check your email to confirm your account!'
  }
  
  loading.value = false
}
</script>

<template>
  <div>
    <h2>Create Account</h2>
    
    <form @submit.prevent="handleRegister">
      <div class="form-group">
        <label for="email">Email</label>
        <input 
          id="email"
          v-model="email" 
          type="email" 
          required 
        />
      </div>
      
      <div class="form-group">
        <label for="password">Password</label>
        <input 
          id="password"
          v-model="password" 
          type="password" 
          required 
          minlength="6"
        />
      </div>
      
      <button type="submit" :disabled="loading">
        {{ loading ? 'Creating account...' : 'Create Account' }}
      </button>
    </form>
    
    <div v-if="error" class="error">
      {{ error }}
    </div>
    
    <div v-if="success" class="success">
      {{ success }}
    </div>
    
    <p style="text-align: center; margin-top: 1rem;">
      Already have an account? 
      <NuxtLink to="/login" style="color: #007bff;">Sign in</NuxtLink>
    </p>
  </div>
</template>

<style scoped>
h2 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: #333;
}

.form-group {
  margin-bottom: 1rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

button {
  width: 100%;
  padding: 0.75rem;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.error {
  margin-top: 1rem;
  padding: 0.75rem;
  background: #fee;
  color: #c33;
  border-radius: 4px;
  text-align: center;
}

.success {
  margin-top: 1rem;
  padding: 0.75rem;
  background: #efe;
  color: #363;
  border-radius: 4px;
  text-align: center;
}
</style>