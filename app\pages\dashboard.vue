<script setup lang="ts">
import type { User } from '@supabase/supabase-js'

definePageMeta({
  middleware: 'auth'
})

const { $supabase } = useNuxtApp()
const user = ref<User | null>(null)

// Get current user
onMounted(async () => {
  const { data: { user: currentUser } } = await $supabase.auth.getUser()
  user.value = currentUser
})

const handleLogout = async () => {
  await $supabase.auth.signOut()
  await navigateTo('/login')
}
</script>

<template>
  <div style="padding: 2rem; max-width: 800px; margin: 0 auto;">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
      <h1>Dashboard</h1>
      <button @click="handleLogout" style="padding: 0.5rem 1rem; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
        Logout
      </button>
    </div>
    
    <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 8px;">
      <h2>Welcome to SECS!</h2>
      <p><strong>Email:</strong> {{ user?.email }}</p>
      <p><strong>User ID:</strong> {{ user?.id }}</p>
      <p>You are successfully logged in. This is where your contractor dashboard will be.</p>
    </div>
  </div>
</template>





