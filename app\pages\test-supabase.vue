<script setup lang="ts">
const { $supabase } = useNuxtApp()
const result = ref<string>('(not tested yet)')
const loading = ref(false)
const error = ref<string | null>(null)
const companies = ref<any[]>([])
const companiesLoading = ref(false)
const companiesError = ref<string | null>(null)

// Form data
const newCompany = ref({
  Name: '',
  Email: '',
  Phone: '',
  Location: '',
  WebsiteURL: ''
})
const createLoading = ref(false)
const createError = ref<string | null>(null)
const createSuccess = ref(false)

const runPing = async () => {
  loading.value = true
  error.value = null
  result.value = '(waiting...)'
  const { data, error: err } = await $supabase.rpc('ping')
  if (err) error.value = err.message
  else result.value = data ?? '(no data)'
  loading.value = false
}

const listCompanies = async () => {
  companiesLoading.value = true
  companiesError.value = null
  companies.value = []
  
  const { data, error: err } = await $supabase
    .from('Company')
    .select('*')
  
  if (err) {
    companiesError.value = err.message
  } else {
    companies.value = data || []
  }
  companiesLoading.value = false
}

const createCompany = async () => {
  createLoading.value = true
  createError.value = null
  createSuccess.value = false
  
  const { data, error: err } = await $supabase
    .from('Company')
    .insert([{
      Name: newCompany.value.Name,
      Email: newCompany.value.Email,
      Phone: newCompany.value.Phone,
      Location: newCompany.value.Location,
      WebsiteURL: newCompany.value.WebsiteURL
    }])
    .select()
  
  if (err) {
    createError.value = err.message
  } else {
    createSuccess.value = true
    // Reset form
    newCompany.value = {
      Name: '',
      Email: '',
      Phone: '',
      Location: '',
      WebsiteURL: ''
    }
    // Refresh companies list
    await listCompanies()
  }
  createLoading.value = false
}
</script>

<template>
  <div style="max-width:600px;margin:2rem auto;font-family:system-ui">
    <h1>Supabase Connectivity Test</h1>
    <p>Click to call <code>rpc('ping')</code> on Supabase.</p>

    <button :disabled="loading" @click="runPing">
      {{ loading ? 'Testing…' : 'Run ping()' }}
    </button>

    <div style="margin-top:1rem">
      <strong>Result:</strong> {{ result }}
      <div v-if="error" style="color:#b00;margin-top:0.5rem">
        Error: {{ error }}
      </div>
    </div>

    <hr style="margin:2rem 0">

    <h2>Create New Company</h2>
    <form @submit.prevent="createCompany" style="margin-bottom:2rem">
      <div style="margin-bottom:1rem">
        <label style="display:block;margin-bottom:0.5rem;font-weight:bold">Name *</label>
        <input 
          v-model="newCompany.Name" 
          type="text" 
          required 
          style="width:100%;padding:0.5rem;border:1px solid #ddd;border-radius:0.25rem"
        >
      </div>
      
      <div style="margin-bottom:1rem">
        <label style="display:block;margin-bottom:0.5rem;font-weight:bold">Email *</label>
        <input 
          v-model="newCompany.Email" 
          type="email" 
          required 
          style="width:100%;padding:0.5rem;border:1px solid #ddd;border-radius:0.25rem"
        >
      </div>
      
      <div style="margin-bottom:1rem">
        <label style="display:block;margin-bottom:0.5rem;font-weight:bold">Phone *</label>
        <input 
          v-model="newCompany.Phone" 
          type="tel" 
          required 
          style="width:100%;padding:0.5rem;border:1px solid #ddd;border-radius:0.25rem"
        >
      </div>
      
      <div style="margin-bottom:1rem">
        <label style="display:block;margin-bottom:0.5rem;font-weight:bold">Location *</label>
        <input 
          v-model="newCompany.Location" 
          type="text" 
          required 
          style="width:100%;padding:0.5rem;border:1px solid #ddd;border-radius:0.25rem"
        >
      </div>
      
      <div style="margin-bottom:1rem">
        <label style="display:block;margin-bottom:0.5rem;font-weight:bold">Website URL *</label>
        <input 
          v-model="newCompany.WebsiteURL" 
          type="url" 
          required 
          style="width:100%;padding:0.5rem;border:1px solid #ddd;border-radius:0.25rem"
        >
      </div>
      
      <button 
        type="submit" 
        :disabled="createLoading"
        style="background:#28a745;color:white;padding:0.75rem 1.5rem;border:none;border-radius:0.5rem;cursor:pointer;font-size:1rem"
      >
        {{ createLoading ? 'Creating…' : 'Create Company' }}
      </button>
    </form>

    <div v-if="createError" style="color:#b00;margin-bottom:1rem">
      Error: {{ createError }}
    </div>
    
    <div v-if="createSuccess" style="color:#28a745;margin-bottom:1rem">
      Company created successfully!
    </div>

    <hr style="margin:2rem 0">

    <h2>Companies</h2>
    <button :disabled="companiesLoading" @click="listCompanies">
      {{ companiesLoading ? 'Loading…' : 'List All Companies' }}
    </button>

    <div v-if="companiesError" style="color:#b00;margin-top:0.5rem">
      Error: {{ companiesError }}
    </div>

    <div v-if="companies.length > 0" style="margin-top:1rem">
      <h3>Found {{ companies.length }} companies:</h3>
      <div v-for="company in companies" :key="company.id" style="border:1px solid #ddd;margin:0.5rem 0;padding:1rem;border-radius:0.5rem">
        <pre>{{ JSON.stringify(company, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<style scoped>
button {
  font-size: 1.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.5rem;
  background: #eee;
  cursor: pointer;
}
</style>


